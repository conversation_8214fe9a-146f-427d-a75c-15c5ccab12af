#launcher {
  width: 100%;
  height: calc(100% - 30px);
  color: #7f7f7f;
  background: #fff;
  background: -moz-linear-gradient(top, #fff 0%, #eee 100%);
  background: -webkit-linear-gradient(top, #fff 0%, #eee 100%);
  background: linear-gradient(to bottom, #fff 0%, #eee 100%);
  filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#fff', endColorstr='#eee', GradientType=0);
  background-repeat: no-repeat;
  background-attachment: fixed;
  font-family: 'Fredoka One';
  user-select: none;
  position: relative;
}

html {
  padding: 8px;
  padding-bottom: 12px
}

html, body {
  border-radius: 12px;
  overflow: hidden;
}

body {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  border: 1px solid #414141;
}  

.darwin body {
  box-shadow: none;
}

#titlebar {
  display: flex;
  user-select: none;
  width: 100%;
  height: 30px;
  background-color: #1b1b1b;
  -webkit-app-region: drag;
  position: relative;
  z-index: 100;
  font-family: 'Munged';
  display: flex;
  justify-content: flex-end;
  box-shadow: inset 0 1px 1px rgb(106 106 106 / 15%);
  color: rgba(255, 255, 255, 0.5);
}

.darwin #titlebar {
  justify-content: flex-start;
}

.darwin #titlebar .controls {
  flex-direction: row-reverse;
}

.darwin #titlebar .controls span {
  width: 14px;
  height: 14px;
}

.darwin #titlebar .controls span:focus i {
  display: block;
}

.darwin #titlebar .controls i {
  font-size: 0.6rem;
  font-weight: 600;
  display: none;
}

.darwin #titlebar .controls:hover span i {
  display: block;
}

.darwin #titlebar .controls #control-minimize i {
  margin-top: -5px;
}

.win32 #titlebar .controls #control-minimize i {
  margin-top: -7px;
}

#titlebar .controls #control-close i {
  margin-bottom: -1px;
}

#titlebar .controls #control-minimize i {
  margin-top: -3px;
}

#titlebar h1.title {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  left: 0;
  width: 100%;
  height: 100%;
  text-align: center;
  font-size: 13px;
}

#titlebar .controls {
  display: flex;
  align-items: center;
  padding-right: 10px;
  -webkit-app-region: no-drag;
  z-index: 100;
}

#titlebar .controls i {
  font-size: 0.8rem;
}

#titlebar .controls span {
  background-color: rgba(255,255,255,0.05);
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 20px;
  width: 20px;
  padding: 2px;
  margin-left: 10px;
}
#titlebar .controls span:hover {
  background-color: rgba(255,255,255,0.13);
}

/* This creates two invisible spacers, one on each side of the title. */
#titlebar .left-spacer {
  flex-grow: 1;
}



* {
  letter-spacing: -0.02em;
}

#blog .alt-content {
  display: none;
}

#no-internet-notice,
.toast {
  padding: 10px;
  color: #fff;
  padding-left: 15px;
  padding-right: 15px;
  border-radius: 999px;
  display: none;
}

#no-internet-notice {
  background-color: #c0392b;
}

.toast {
  margin-top: 15px;
  background-color: #404040;
}

#no-internet-notice i,
.toast i {
  float: left;
  margin-right: 6px;
  margin-top: -4px;
}

body.no-internet #no-internet-notice {
  display: block;
}

body.no-internet #about-page button {
  display: none;
}

body.no-internet #about-page .alt-content-small {
  display: block;
}

body #about-page .alt-content-small {
  display: none;
}

body #about-page ul.columns {
  column-count: 3;
  margin-top: -20px;
  text-align: left;
}

body #about-page ul.columns li {
  list-style: none;
} 

#launcher #sidebar {
  float: left;
  width: 230px;
  height: 100%;
  position: relative;
  -webkit-box-shadow: 10px 0 60px -2px rgba(0, 0, 0, 0.1);
  box-shadow: 10px 0 60px -2px rgba(0, 0, 0, 0.1);
  z-index: 12;
  background: #fff;
  background: -moz-linear-gradient(top, #fff 0%, #eee 100%);
  background: -webkit-linear-gradient(top, #fff 0%, #eee 100%);
  background: linear-gradient(to bottom, #fff 0%, #eee 100%);
  filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#fff', endColorstr='#eee', GradientType=0);
  background-repeat: no-repeat;
  background-attachment: fixed;
}

#launcher #content {
  float: left;
  width: calc(100% - 230px);
  height: 100%;
  position: relative;
  overflow-y: hidden;
  overflow-x: hidden;
  border-top-left-radius: 12px;
}

div.page {
  background-size: cover;
  background-position: center center;
  height: 100%;
  width: 100%;
  position: relative;
  display: block;
}

body.splash {
  filter: blur(5px) !important;
}

.page-content {
  overflow-y: auto;
  overflow-x: hidden;
  height: calc(100% - 89px);
  padding-bottom: 20px;
  padding-top: 10px;
  margin-top: -10px;
}

#home-page .page-content {
  padding-bottom: 0px;
}

block {
  display: block;
}

.material-icons.left {
  float: left;
  margin-right: 8px;
  margin-top: -5px;
}

#sidebar svg[class$="-icon"] {
  margin-left: 6px;
  margin-right: 7px;
}

div#splash {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.4);
  filter: blur(100px);
  z-index: 11;
}

div#splash > div {
  color: #fff;
  font-size: 24px;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
  text-align: center;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -10px;
  margin-left: -100px;
  width: 200px;
  height: 20px;
}

#social-icons {
  float: right;
}

#social-icons > i {
  margin-right: 5px;
}

.button.material-icons {
  width: 30px;
  height: 30px;
  text-align: center;
  padding: 7px;
}

a.button {
  font-size: inherit;
  text-decoration: none;
  font-size: 15px;
}

div.page > h1.jumbotron {
  font-size: 22px;
  padding: 40px;
  color: #fff;
  background: linear-gradient(180deg, #4e90f3 0%, #3877d8 100%);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  width: calc(100% + 1px);
  position: relative;
  z-index: 100;
  height: 100px;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  border-top-left-radius: 12px;
}

div.page > h1.jumbotron span {
  display: block;
  margin-top: -10px;
  text-transform: uppercase;
}

div.page > h1.jumbotron small {
  font-size: 14px;
  font-family: 'Munged';
  font-weight: 400;
}

div.error,
div.notice {
  padding: 15px;
  font-family: 'Munged';
  font-size: 14px;
  display: block;
  text-align: center;
  color: #fff;
  line-height: 22px;
}

div#ponline {
  text-align: center;
  display: block;
  width: 100%;
  margin-top: -5px;
  margin-bottom: 20px;
  color: rgba(0, 0, 0, 0.4);
}

div.error {
  background-color: #c93507;
}

.notice {
  background-color: #f1c40f;
}

div.page p, div.page .readable-font {
  padding: 20px;
  font-family: 'Munged';
  font-size: 14px;
  line-height: 22px;
  text-align: justify;
  hyphens: auto;
  word-break: break-word;
}

.selectable-text {
  user-select: text;
}

#launcher #sidebar li#logo {
  padding: 20px;
  width: 180px;
  height: 100px;
  margin: 0 auto;
  margin-top: 20px;
  background: url(images/logo.png) no-repeat;
  margin-bottom: 40px;
  border-bottom: 1px solid rgba(0, 0, 0, 0);
  padding-top: 70px;
  text-align: center;
}

#logo:after {
  content: 'OPEN BETA';
}

#sidebar div.bottom {
  position: absolute;
  bottom: 0px;
  left: 0px;
  width: 100%;
}
#sidebar div.bottom li:last-child {
  margin-bottom: 8px!important;
}
#sidebar div.bottom li:last-child:after {
  height: 1px;
  width: calc(100% + 16px);
  background: rgba(0, 0, 0, 0.1);
  position: absolute;
  content: '';
  left: -8px;
  top: -8px;
  border-radius: 6px;
}

#sidebar div.bottom li:last-child {
  margin-top:14px;
}

#launcher #sidebar li:not(#logo) {
  transition: background 0.4s, color 0.4s, padding-left 0.4s;
  -webkit-app-region: no-drag;
  margin-left: 8px;
  margin-right: 8px;
  margin-bottom: 5px;
  border-radius: 6px;
  padding: 13px;
  font-size: 1.07rem;
}

#launcher #sidebar li.active,
#launcher #sidebar li:not(#logo):hover {
  border-right: 4px solid #4C8DEE;
  background-color: rgba(0, 0, 0, 0.035);
  color: #4C8DEE;
  cursor: pointer;
  padding-left: 20px!important;
}
#launcher #sidebar li path {
  fill: #7f7f7f;
  transition: fill 0.4s;
}
#launcher #sidebar li:not(#logo):hover path,
#launcher #sidebar li.active path {
  fill: #4C8DEE;
}
#launcher #sidebar li {
  text-transform: uppercase;
}

#launcher #content #full-install {
  position: absolute;
  width: 100%;
  top: -20px;
  height: calc(100% + 20px);
  background: url(images/city.jpg) no-repeat;
  background-position: center center;
  background-size: cover;
  z-index: 10;
  display: none;
}

#launcher #content #full-install .bottom {
  height: 170px;
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
  z-index: 10;
  color: #fff;
  background-image: linear-gradient(to bottom, transparent, #000);
  padding: 30px;
}

#launcher #content #full-install .bottom h1 {
  font-size: 24px;
  font-weight: normal;
  margin-bottom: 10px;
}

#launcher #content #full-install .bottom p {
  font-family: 'Munged';
  font-size: 14px;
  font-weight: 400;
  margin-bottom: 15px;
  padding: 0;
}

.backdrop {
  height: 215px;
  width: 100%;
  background-image: url(images/backdrop.png);
  background-position: center center;
  background-size: cover;
}

.loading {
  width: 100%;
  height: 5px;
  background-color: #fff;
  margin-bottom: 20px;
  max-width: 400px;
  border-radius: 999px;
}

@-webkit-keyframes animate-stripes {
  100% {
    background-position: 100px 0px;
  }
}

@keyframes animate-stripes {
  100% {
    background-position: 100px 0px;
  }
}

.download.stopped .loading .progress {
  -webkit-animation: none;
  animation: none;
}

.download.stopped .loading .progress {
  background-image: none;
  background: #4C8DEE;
}

.loading .progress {
  transition: width .5s ease-in-out;
  float: left;
  width: 0%;
  height: 5px;
  overflow: hidden;
  background-image: -webkit-linear-gradient(
      -45deg,
      transparent 33%,
      rgba(255, 255, 255, 0.3) 33%,
      rgba(255, 255, 255, 0.3) 66%,
      transparent 66%
    ),
    -webkit-linear-gradient(top, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1)),
    -webkit-linear-gradient(left, #4C8DEE, #4C8DEE);
  border-radius: 999px;
  background-size: 35px 20px, 100% 100%, 100% 100%;
  -webkit-animation: animate-stripes 5s linear infinite;
  animation: animate-stripes 5s linear infinite;
}

#exit {
  position: absolute;
  top: 5px;
  right: 10px;
  color: #fff;
  font-size: 30px;
  -webkit-app-region: no-drag;
  cursor: pointer;
}

.FAQ {
  padding: 20px;
}

.FAQ p {
  max-width: 500px;
  hyphens: auto;
}

strong {
  font-family: 'Fredoka One';
}

[data-keyboard-user] a[href]:focus, 
[data-keyboard-user] select:focus, 
[data-keyboard-user] input:focus, 
[data-keyboard-user] [tabindex]:focus:not([install]) {
  outline: none; 
  box-shadow: 0 0 0 3px rgba(76, 141, 238,0.5); /* Blue focus ring */
}

[data-keyboard-user] button:not(.icon-only):focus,
[data-keyboard-user] .button:not(.icon-only):focus {
  box-shadow: 
  0 0 0 3px rgba(76, 141, 238,0.5), /* Blue focus ring */
  inset 8px 8px 8px rgba(255, 255, 255, 0.2),
  inset -2px -2px 2px rgba(0, 0, 0, 0.2), 
  0 1px 3px rgba(0, 0, 0, 0.12),
  0 1px 2px rgba(0, 0, 0, 0.24)!important;
}

[data-keyboard-user] button.icon-only:focus,
[data-keyboard-user] .button.icon-only:focus {
  outline: none; 
  box-shadow: 0 0 0 3px rgba(76, 141, 238,0.5)!important; /* Blue focus ring */
}

button,
.button {
  padding: 10px;
  padding-left: 12px;
  padding-right: 12px;
  font-family: inherit;
  text-align: center;
  border: 0;
  color: #fff;
  background-color: #43a900;
  border-radius: 99px;
  cursor: pointer;
  outline: 0;
  transition: background-color 0.4s;
  font-size: 16px !important;
  box-shadow: inset 8px 8px 8px rgba(255, 255, 255, 0.2),
    inset -2px -2px 2px rgba(0, 0, 0, 0.2), 0 1px 3px rgba(0, 0, 0, 0.12),
    0 1px 2px rgba(0, 0, 0, 0.24);
}

body button.icon-only {
  padding: 3px !important;
  padding-left: 3px !important;
  padding-right: 3px !important;
  background-color: transparent !important;
  background: transparent !important;
  border-radius: 0% !important;
  box-shadow: none !important;
  opacity: 0.6;
  color: #fff;
}

body button.icon-only i {
  margin-right: 0!important;
}

button.icon-only:hover {
  opacity: 1;
}

button:hover,
.button:hover {
  background-color: #52cc00;
}

button:not([disabled]):active,
.button:not([disabled]):active {
  position: relative;
  top: 1px;
}

button.launch,
.button.launch {
  display: block;
  margin: 5px;
  font-size: 14px;
  width: 150px;
  margin: 0 auto;
  margin-top: -20px;
  margin-bottom: 20px;
  border-radius: 999px;
}

#downloads > div {
  width: 100%;
  float: left;
  position: relative;
}

button.blue,
.button.blue {
  background-color: #4C8DEE;
}

button.blue:hover,
.button.blue:hover {
  background-color: #2daac4;
}

#downloads div.item div.tick {
  position: absolute;
  right: -10px;
  top: -10px;
}

#downloads div.item div.tick.installed {
  display: none;
}

#downloads div.installed.item div.tick.installed {
  width: 45px;
  height: 45px;
  background-image: url(images/icon_installed.png);
  background-repeat: no-repeat;
  background-size: contain;
  display: block !important;
}

#downloads > div > .item {
  height: 273px;
  width: 100%;
  position: relative;
  width: 100%;
  background-position: center center;
  transition: background-size 0.3s;
  background-size: cover;
  border-right: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
}

#installer-page .page-content {
  padding-bottom: 0;
}

#downloads > div > .item:hover,
#downloads > div > .item:focus {
  background-size: 90% !important;
}

#downloads > div.block > .item:hover,
#downloads > div.block > .item:focus {
  background-size: 35% !important;
}

#downloads > div > .item[install='MacExtras']:hover,
#downloads > div > .item[install='MacExtras']:focus {
  background-size: 70% !important;
}
#downloads > div > .item[install='Mono'] {
  background-color: #fff !important;
}

#downloads > div > .item[install='Mono']:hover,
#downloads > div > .item[install='Mono']:focus {
  background-size: 120% !important;
}

.item.recent .ribbon-wrapper {
  display: block;
}

.ribbon-wrapper {
  display: none;
  width: 85px;
  height: 88px;
  overflow: hidden;
  position: absolute;
  top: -3px;
  right: -3px;
}

.ribbon {
  text-align: center;
  transform: rotate(45deg);
  position: relative;
  padding: 7px 0;
  left: -5px;
  top: 15px;
  width: 120px;
  letter-spacing: 1px;
  background-image: -webkit-linear-gradient(top, #ff1c1c, #c72626);
  color: #fff;
  box-shadow: 0px 0px 3px rgba(0,0,0,0.3);
}

.ribbon:before, .ribbon:after {
  content: "";
  border-top: 3px solid #a00606;   
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  position: absolute;
  bottom: -3px;
}

.ribbon:before {
  left: 0;
}
.ribbon:after {
  right: 0;
}

::-webkit-scrollbar {
  width: 5px;
  padding: 3px;
}

::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background: rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-thumb:active {
  background: rgba(0, 0, 0, 0.5);
}

::-webkit-scrollbar-track {
  background: #ddd;
}

#downloads > div .tag {
  color: #fff;
  background-image: -webkit-linear-gradient(#fafafa, #f4f4f4 40%, #e5e5e5);
  display: inline-block;
  position: absolute;
  bottom: 45px;
  left: 45px;
  margin: 0 auto;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  color: #595959;
  width: 300px;
  border-radius: 8px;
  transition: box-shadow 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

#downloads > div .tag h1 {
  font-size: 20px;
  color: #4C8DEE;
  margin-bottom: 10px;
  transition: color 0.2s;
}

#downloads > div .tag span {
  font-family: 'Munged';
  font-size: 14px;
  line-height: 22px;
}

#downloads > div:hover .tag h1 {
  color: #5298ff;
}

input.error,
select.error {
  border: 2px solid #c93507;
  color: #c93507;
}

input,
select {
  animation-name: appear;
  animation-duration: 500ms;
  animation-timing-function: ease-out;
  animation-fill-mode: both;
  font-family: inherit;
  font-size: inherit;
  color: inherit;
  border: 0;
  border-radius: 8px;
  background-image: -webkit-linear-gradient(#fafafa, #f4f4f4 40%, #e5e5e5);
  background-position: 97% center;
  background-repeat: no-repeat;
  border: 1px solid #aaa;
  width: 100%;
  font-family: Munged;
  padding: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  outline: 0;
}

select {
  -webkit-appearance: none;
  background-image: url(images/15xvbd5.png),
    -webkit-linear-gradient(#fafafa, #f4f4f4 40%, #e5e5e5);
}

#settings-page .form {
  padding: 30px;
  width: 100%;
  margin-bottom: -30px;
  float: left;
}

#settings-page .form h1 {
  padding: 10px;
  color: #4C8DEE;
  letter-spacing: 2px;
  text-transform: uppercase;
  margin-bottom: 20px;
  padding-left: 0;
  font-size: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

#settings-page .form div {
  margin-bottom: 20px;
}

#settings-page .form div span {
  margin-bottom: 10px;
  display: block;
  font-family: 'Munged';
  font-size: 16px;
  color: #898989;
}

#settings-page .form div small {
  display: block;
  line-height: 20px;
  font-family: 'Munged';
  font-size: 12px;
  margin-top: 10px;
  color: rgba(0, 0, 0, 0.4);
}

#widget {
  padding: 20px;  
}

#simtime-container {
  border-radius: 6px;
  background: rgba(0, 0, 0, 0.03);
  padding: 5px;
  padding-left: 0px;
  width: 100px;
  text-align: center;
  margin: 0 auto;
  margin-top: -8px;
  margin-bottom: 8px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.6);
}

#simtime-container i,
#simtime-container #simtime {
  display: inline-block;
  vertical-align: middle;
}
#simtime-container #simtime {
  font-family: 'Munged';
}
#simtime-container i {
  margin-right: 5px;
  font-size: 20px;
}

@keyframes ghostlyLeft {
  0% {
    margin-left: 0px;
  }
  50% {
    margin-left: 20px;
  }
  100% {
    margin-left: 0px;
  }
}

@keyframes ghostlyUp {
  0% {
    margin-top: 0px;
  }
  50% {
    margin-top: 20px;
  }
  100% {
    margin-top: 0px;
  }
}

.hint.arrow-left {
  animation: ghostlyLeft 1.5s 4s infinite;
  animation-delay: 0.25s;
}

.hint.arrow-top {
  animation: ghostlyUp 1.5s 4s infinite;
  animation-delay: 0.25s;
}

.hint {
  color: #fff;
  background-color: #43a900;
  padding: 20px;
  font-size: 12px;
  display: none;
  position: absolute;
  z-index: 11;
  max-width: 200px;
  line-height: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.4s;
}

.hint:hover {
  color: rgba(255, 255, 255, 0.8);
}

.hint h1 {
  display: block;
  font-size: 14px;
  font-family: inherit;
  margin-bottom: 5px;
}

.hint small {
  font-family: 'Munged';
  line-height: 1rem;
  display: inline-block;
  font-size: 0.8rem !important;
}

.hint.arrow-top {
  text-align: center;
}

.hint.arrow-left:after,
.hint.arrow-left:before {
  right: 100%;
  top: 50%;
  border: solid transparent;
  content: ' ';
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}

.hint.arrow-left:after {
  border-color: rgba(136, 183, 213, 0);
  border-right-color: #43a900;
  border-width: 17px;
  margin-top: -17px;
}

.hint.arrow-left:before {
  border-color: rgba(194, 225, 245, 0);
  border-right-color: #3d9900;
  border-width: 23px;
  margin-top: -23px;
}

.hint.arrow-top:after,
.hint.arrow-top:before {
  bottom: 100%;
  left: 50%;
  border: solid transparent;
  content: ' ';
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}

.hint.arrow-top:after {
  border-color: rgba(136, 183, 213, 0);
  border-bottom-color: #43a900;
  border-width: 17px;
  margin-left: -17px;
}

.hint.arrow-top:before {
  border-color: rgba(194, 225, 245, 0);
  border-bottom-color: #3d9900;
  border-width: 23px;
  margin-left: -23px;
}

#toasts {
  position: absolute;
  bottom: 20px;
  right: 20px;
  z-index: 15;
}

.modal {
  width: 400px;
  background-image: -webkit-linear-gradient(#fafafa, #f4f4f4 40%, #e5e5e5);
  background-position: 97% center;
  background-repeat: no-repeat;
  box-shadow: 0 19px 38px rgba(0, 0, 0, 0.1), 0 15px 12px rgba(0, 0, 0, 0.22);
  border-radius: 12px;
  z-index: 199;
  position: fixed;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  padding: 20px;
  padding-top: 30px;
  min-height: 250px;
  max-height: 400px;
  display: flex;
  flex-direction: column;
}

.modal h1 {
  font-size: 24px;
  color: #4C8DEE;
  margin-bottom: 20px;
}

.modal.modal-success h1:before {
  content: "";
  width: 30px;
  height: 30px;
  background-image:url(images/icon_success.png);
  background-repeat: no-repeat;
  background-size:contain;
  float:left;
  margin-right: 10px;
  margin-top:-2px;
}
.modal.modal-error h1:before {
  content: "";
  width: 30px;
  height: 30px;
  background-image:url(images/icon_error.png);
  background-repeat: no-repeat;
  background-size:contain;
  float:left;
  margin-right: 10px;
  margin-top:-2px;
}

.modal p {
  font-family: 'Munged';
  word-break: break-word;
  flex: 1;
  font-size: 0.96rem;
  line-height: 1.3rem;
}

.modal div {
  display: flex;
  justify-content: flex-end;
  padding: 10px;
  padding-top: 0;
}

.modal div button {
  padding-right: 20px;
  padding-left: 20px;
  margin-right: 15px;
  text-transform: uppercase;
}

.modal div span {
  cursor: pointer;
  align-self: center;
}

#overlay {
  position: absolute;
  background: rgba(0, 0, 0, 0.3);
  width: 100%;
  height: 100%;
  z-index: 199;
  display: none;
  cursor: pointer;
  border-top-left-radius: 12px;
  border-bottom-right-radius: 12px;
}

#downloads-page div.download {
  padding: 30px;
  padding-left: 40px;
  padding-right: 40px;
  font-size: 14px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background: #ffffff;
  display: block;
}

#downloads-page div.download:hover {
  background: rgba(255, 255, 255, 0.4);
}

#downloads-page div.download h1 {
  margin-bottom: 10px;
  font-size: 16px;
  font-family: 'Munged';
}

#downloads-page div.download span {
  color: rgba(0, 0, 0, 0.5);
  display: block;
  margin-bottom: 30px;
  font-family: 'Munged';
  line-height: 20px;
  font-size: 0.85rem;
}

#downloads-page div.download .info {
  font-family: 'Munged';
}

#downloads-page div.download .loading {
  margin-top: 20px;
  background: #dfdedf;
}

.miniconsole {
  font-size: 12px;
  height: 150px;
  overflow-y: auto;
  display: none;
  font-family: 'Munged';
  margin-top: 10px;
  margin-bottom: 10px;
  max-width: 100%;
}

.alt-content {
  text-align: center;
  width: 100%;
  display: block;
  padding: 20px;
  font-size: 20px;
}

.alt-content i {
  color: rgba(0, 0, 0, 0.4);
  font-size: 22px;
}

.alt-content-small {
  width: 100%;
  display: block;
  padding: 10px;
  font-size: 18px;
}

a {
  color: inherit;
}

#tip {
  background: rgba(0, 0, 0, 0.45);
  color: #fff;
  padding: 15px;
  width: 250px;
  border-radius: 6px;
  margin-top: 110px;
}

#tip i {
  float: left;
  margin-right: 5px;
  margin-top: -5px;
}

#tip h1 {
  margin-bottom: 5px;
  display: block;
  padding-bottom: 5px;
}

#tip span {
  font-family: 'Munged';
  font-size: 14px;
}

#notifications-page div.notification {
  padding: 30px;
  padding-left: 40px;
  padding-right: 40px;
  font-size: 14px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  background: #ffffff;
  display: block;
}

#notifications-page div.notification h1 {
  font-size: 17px;
  margin-bottom: -10px;
  font-family: 'Fredoka One';
  color: rgba(0, 0, 0, 0.5);
}

#notifications-page div.notification i {
  float: left;
  display: inline-block;
  margin-left: -27px;
  display: none;
  height: 100px;
  margin-top: -5px;
}

#notifications-page div.notification p {
  color: rgba(0, 0, 0, 0.5);
  display: block;
  font-family: 'Munged';
  text-decoration: none !important;
  padding-left: 0;
}

#notifications-page div.notification .notification-link {
  float: right;
  font-family: 'Munged';
  text-decoration: none;
  border-bottom: 1px dotted;
}

#notifications-page div.notification span {
  font-family: 'Munged';
}

#notifications-page div.notification:hover {
  background: rgba(255, 255, 255, 0.4);
}

.win32 .win32-only,
.darwin .darwin-only,
.linux .linux-only {
  display: block!important;
}
.win32 .darwin-only,
.linux .darwin-only,
.darwin .win32-only,
.linux .win32-only {
  display: none;
}

.item .stag {
  position: absolute;
  top: 20px;
  background-color: #55C80A;
  font-size: 12px;
  padding: 4px;
  border-radius: 20px;
  left: 20px;
  color: #fff;
  padding-left: 8px;
  padding-right: 8px;
}

#launcher #sidebar li:not(#logo) {
  position:relative!important;
}

#launcher #sidebar li:not(#logo):hover,
#launcher #sidebar li:not(#logo).active
 {
  border-right:0!important;
}

#launcher #sidebar li.active:before,
#launcher #sidebar li:not(#logo):hover:before {
  height: 50%;
  width:4px;
  background: #4C8DEE;
  position: absolute;
  content: '';
  left: 0;
  top: 25%;
  border-radius:6px;
}

@keyframes spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
i.spin {
  -webkit-animation: spin 2s infinite linear;
  animation: spin 2s infinite linear;
}

#refresh-home-button i {
  font-size: 21px;
  margin-bottom: -2px;
  transition: 1s ease-in-out;
}

.oneclick-install {
  width: 403px;
  height: 504px;
  position: fixed;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  box-shadow: 1px 7px 4px rgba(0, 0, 0, 0.25);
  z-index: 200;
  background-image: 
    linear-gradient(rgba(255, 255, 255, 1), rgba(255, 255, 255, 0)), 
    linear-gradient(90deg, rgb(50 136 195 / 1) 0%, #44ABED 50.31%, #5BD536 100%);
  border-radius: 16px;
  padding: 6px;
    -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.oneclick-install-content {
  background: linear-gradient(180deg, #FFFFFF 0%, #EEEEEE 100%);
  border-radius: 12px;
  padding: 35px;
  display: flex;
  flex-direction:column;
  width: 100%;
  height: 100%;
}

.oneclick-install-text {
  text-align: center;
  flex: 1;
}
.oneclick-install-text h1 {
  font-style: normal;
  font-weight: 400;
  font-size: 26px;
  line-height: 31px;
  letter-spacing: -0.02em;
  background: linear-gradient(90deg, #3288C3 0%, #44ABED 50.31%, #5BD536 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

.oneclick-install-text p, .oneclick-install-select p, .oneclick-install-close {
  font-family: 'Munged';
  margin-top: 10px;
  color: #7f7f7f;
  font-size: 14px;
  line-height: 16px;
}

.oneclick-install-close {
  text-align: center;
  cursor: pointer;
}
.oneclick-install-close:hover {
  color: #a3a1a1;
}

.oneclick-install-select {
  text-align: center;
  border: 1px dashed rgba(0, 0, 0, 0.25);
  border-radius: 20px;
  height: 256px;
  width: 256px;
  display: flex;
  flex-direction: column;
  margin: 0 auto;
  margin-bottom: 20px;
  padding: 20px;
  gap: 15px;
  justify-content: center;
  cursor: pointer;
}

.oneclick-install-select svg {
  margin: 0 auto;
}

.oneclick-install .oneclick-install-select button {
  letter-spacing: normal;
  margin: 0 auto;
  width: 150px;
  text-transform: uppercase;
  font-style: normal;
  font-weight: 400;
  font-size: 16px;
  line-height: 19px;
  text-align: center;
  text-shadow: none!important;
  color: #FFFFFF;
  background: linear-gradient(90deg, #3288C3 0%, #44ABED 50.31%, #5BD536 100%)!important;
  box-shadow: none;
}

.oneclick-install-select:hover p {
  color: #a3a1a1;
}

.oneclick-install-selected .oneclick-install-confirm,
.oneclick-install-selected .oneclick-install-folder {
  display: block!important;
}

.oneclick-install-selected .oneclick-install-help,
.oneclick-install-selected .oneclick-install-plus {
  display: none!important;
}

#sidebar li.dot i {
  position: relative;
}

#sidebar li.dot i::before {
  content: '';
  height: 8px;
  width: 8px;
  border-radius: 10px;
  background: red;
  position: absolute;
  display: block;
  top: 0;
  left: 0;
}

#settings-page input[type='number']::-webkit-inner-spin-button,
#settings-page input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

#blog {
  width: 50%;
}

#blog-link a {
  font-family: 'Munged';
  display: block;
  font-size: 0.85rem;
  border: 2px solid rgb(187 187 187 / 30%);
  padding: 15px;
  border-radius: 4px;
  text-align: center;
  margin-bottom: 0;
  margin: 20px;
}

#widget {
  width: 50%;
  height: 100%;
}

#blog {
  height: 100%;
  overflow-y: auto;
}

.tw {
  height: 100%;
}

#widget {
  max-height: 100%;
}

#downloads > div,
#settings-page .form {
  width: 50%;
}

#downloads > div.block {
  width: 100%;
}

#now-trending {
  border-radius: 8px;
  border: 1px solid rgba(193, 193, 193, 0.27);
  overflow: hidden;
}

#now-trending {
  display: flex;
  height: 100%;
  flex-direction: column;
  background: rgba(0, 0, 0, 0.05);
}

#now-trending .content {
  flex: 1;
  overflow-y: auto;
}
#now-trending .top {
  display: flex;
  padding: 20px;
  padding-top: 25px;
  padding-bottom: 25px;
  background-image: url(images/widget_header.png);
  background-position: center;
  background-size: cover;
  color: #fff;
  align-items: center;
  box-shadow: 0px 3px 3px 0px rgba(255, 255, 255, 0.25) inset;
} 

#now-trending .top h1 {
  flex: 1;
  font-size: 1.4rem;
  text-shadow: 0px 2px 3px black;
}

#now-trending .top span {
  font-family: 'Munged';
  font-size: 0.8rem;
  background: rgba(0,0,0,0.6);
  border-radius: 50px;
  padding: 5px;
  padding-left: 10px;
  padding-right: 10px;
}

#now-trending li:last-child {
  border-bottom: 0;
}

#now-trending li {
  padding: 14px;
  display: flex;
  align-items: center;
  border-top: 1px solid rgba(193, 193, 193, 0.27);
}

#now-trending li:nth-child(even) {
  /* background: rgb(30 58 82 / 2%); */
}

#now-trending li .icon {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 20px;
  height: 40px;
  position: relative;
  width: 40px;
  color: #10171E;
  margin-right: 30px;
  position: relative;
  filter: brightness(1.2);
}
#now-trending li .icon .lot {
  width: 80px;
  position: absolute;
  left: -15px;
  filter: drop-shadow(4px 4px 2px rgba(0, 0, 0, 0.25));
}
#now-trending li .info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
#now-trending li .info .lot-name {
  margin-bottom: 0px;
  letter-spacing: -0.3px;
  padding-bottom: 5px;
  background: linear-gradient(90deg, #4C8DEE 0%, #33e11f 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 1.08rem;
}
#now-trending .hot .busy {
  display: inline-block;
}
#now-trending .busy {
  display: none;
  background-image: linear-gradient(to bottom, #f80707 0%, #ffb000 55.4375%, #ff0000 100%);
  background-size: 150% 150%;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
#now-trending li .info .owner i {
  font-size: 1.2rem;
  margin-right: 3px;
  margin-left: -5px;
}
#now-trending li .info .owner {
  font-family: 'Munged';
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  color: rgb(255 255 255 / 60%);
}

#now-trending li .players {
  display: flex;
  align-items: center;
}

#now-trending li .players span {
  display: flex;
  align-items: center;
  color: #10171e;
  padding: 2px;
  /* padding-left: 8px; */
  width: 50px;
  font-size: 0.9rem;
  text-align: center;
  display: flex;
  justify-content: center;
  border-radius: 160px;
  background: linear-gradient(90deg, #2D8AC4 0%, #54D035 100%);
  margin-left: 5px;
}

#now-trending li .icon .avatar {
  position: absolute;
  left: -5px;
  top: -18px;
  height: 69px;
  width: 17px;
  background-position: -198px;
  background-size: cover;
}

#now-trending li .players span i {
  margin-right: 5px;
  font-size: 1.2rem;
}

.installed .item-info {
  display: block;
}
.item-info {
  z-index: 1;
  padding: 15px;
  width: 100%;
  font-size: 0.9rem;
  cursor: initial;
  display: none;
}

.item-info i {
  font-size: 1.4rem;
}

.item-info:hover .text {
  display: block;
}

.item-info .container {
  width: 100%;
  display: flex;
  padding: 5px;
  align-items: center;
  justify-content: flex-end;
  border-radius: 99px;
  padding-left: 15px;
  padding-right: 8px;
}

.item-info .container .material-icons {
  cursor: pointer;
}

.item-info .container .material-icons {
  color: rgba(0,0,0,0.3);
}
.item-info .container .material-icons:hover {
  color: rgba(0,0,0,0.5);
}

ul#sidebar li[page-trigger="downloads"].has-downloads::after {
  display: flex;
  align-items: center;
  justify-content: center;
  content: var(--unread-progress-items, '');
  font-family: 'Munged';
  font-weight: bold;
  font-size: 0.6rem;
  background: rgb(0 0 0 / 9%);
  width: 14px;
  height: 14px;
  border-radius: 4px;
  margin-left: 10px;
  position: absolute;
  top: 4px;
  left: -3px;
  transition: 0.4s left;
}

ul#sidebar li[page-trigger="downloads"].has-downloads:hover::after,
ul#sidebar li[page-trigger="downloads"].has-downloads.active::after {
  left: 4px;
}

.scenario-stage {
  background-size: contain;
  background-position: right;
  height: 100%;
  width: 650px;
  position: absolute;
  top: 0px;
  right: -100px;
  background-repeat: no-repeat;
  filter: drop-shadow(4px -2px 2px rgba(0, 0, 0, 0.25));
}

#blog {
  /* padding: 10px; */
}

#blog-link {
  margin-top: 10px;
}
.article:nth-child(even) {
  /*background-color: rgba(0, 0, 0, 0.1);*/
}

.article {
  display: flex;
  flex-direction: row;
  align-items: flex-start; /* Add this line */
  padding: 25px;
}

.article-title {
  font-size: 1.3rem;
  font-family: 'Fredoka One';
  line-height: 1.4rem;
  color: #4C8DEE;
  cursor: pointer;
}

.article-title:hover {
  filter: brightness(1.2);
}

.article-image {
  width: 100px;
  height: 100px;
  background-size: cover;
  border-radius: 24px;
  background-position: center center;
  box-shadow: 0px 3px 3px 0px rgba(255, 255, 255, 0.3) inset;
  /* overflow: hidden; */
  cursor: pointer;
}

.article-image:hover:before {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.05);
  height: 100%;
  border-radius: 24px;
  width: 100%;
  content: 'open_in_new';
  font-size: 1.3rem;
  z-index: 2;
  color: rgba(255, 255, 255, 1);
  font-family: 'Material Icons';
  font-weight: 600;
}

.article-date {
  margin-top: 10px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}
.article-date i {
  font-size: 1.2rem;
  margin-right: 5px;
}
.article-date span {
  font-size: 0.85rem;
}

.article-content {
  margin-left: 20px;
  display: flex;
  flex-direction: column;
  flex: 1;
  font-family: 'Munged';
}

.article-excerpt {
  font-size: 0.85rem;
  line-height: 1.1rem;
  text-align: justify;
  hyphens: auto;
  color: #9f9f9f;
}

.article-author {
  display: none;
  align-items: center;
  font-size: 0.85rem;
  margin-top: 5px;
}
.article-author i {
    font-size: 1.3rem;
  margin-right: 3px;
  margin-left: -5px;
}

/* Slight dark/light theme modifications */

.open_beta #now-trending .top span,
.turbo #now-trending .top span,
.simitone #now-trending .top span   {
  background-color: rgba(255,255,255,0.5);
  color: rgba(0,0,0,0.9);
}

.open_beta #now-trending .top,
.turbo #now-trending .top,
.simitone #now-trending .top {
  filter: brightness(120%);
}

.open_beta #now-trending .players,
.turbo #now-trending .players,
.simitone #now-trending .players {
  filter: brightness(105%);
}

.open_beta #now-trending li .info .lot-name,
.turbo #now-trending li .info .lot-name,
.simitone #now-trending li .info .lot-name {
  filter: saturate(1.2);
}

.open_beta #now-trending .owner *,
.turbo #now-trending .owner *,
.simitone #now-trending .owner * {
  color: rgb(39 50 65 / 62%);
}

.open_beta #now-trending .players *,
.turbo #now-trending .players *,
.simitone #now-trending .players * {
  color: #fff;
}

.open_beta #now-trending,
.turbo #now-trending,
.simitone #now-trending,
.open_beta #now-trending li,
.turbo #now-trending li,
.simitone #now-trending li {
  border-color: rgba(0,0,0,0.1);
  background: transparent;
}

.dark .item-info .container .material-icons,
.halloween .item-info .container .material-icons,
.indigo .item-info .container .material-icons {
 color: rgba(255,255,255,0.3)
}
.dark .item-info .container .material-icons:hover,
.halloween .item-info .container .material-icons:hover,
.indigo .item-info .container .material-icons:hover {
  color: rgba(255,255,255,0.5)
}

.dark ul#sidebar li[page-trigger="downloads"].has-downloads::after,
.halloween ul#sidebar li[page-trigger="downloads"].has-downloads::after,
.indigo ul#sidebar li[page-trigger="downloads"].has-downloads::after {
  background: rgba(0, 0, 0, 0.3);
}

.open_beta #titlebar,
.turbo #titlebar,
.simitone #titlebar {
  background-color: #fff;
  color: rgba(0, 0, 0, 0.55);
}

.win32 .open_beta #titlebar h1.title,
.win32 .turbo #titlebar h1.title,
.win32 .simitone #titlebar h1.title {
  -webkit-text-stroke: 0.2px rgba(0, 0, 0, 0.55); 
}



.open_beta #titlebar .controls span,
.turbo #titlebar .controls span,
.simitone #titlebar .controls span {
  background-color: rgba(0,0,0,0.1);
}

.open_beta #titlebar .controls span:hover,
.turbo #titlebar .controls span:hover,
.simitone #titlebar .controls span:hover {
  background-color: rgba(0,0,0,0.15);
}

body.open_beta,
body.turbo,
body.simitone {
  border-color: #a5a5a5;
}  

.darwin body.open_beta,
.darwin body.turbo,
.darwin body.simitone {
  border-color: transparent;
}

.open_beta .article:nth-child(even),
.turbo .article:nth-child(even),
.simitone .article:nth-child(even) {
  /* background-color: rgb(30 58 82 / 2%); */
}


.article {
  position: relative;
  border-bottom: 1px dashed rgba(193, 193, 193, 0.27);
  background: rgba(0,0,0,0.05);
}
.article:last-child {
  /* border-bottom: 0; */
}
.open_beta .article,
.turbo .article,
.simitone .article {
  border-color: rgba(0, 0, 0, 0.1);
  background: transparent;
}

.article-image {
  position: relative;
}
.article-image::after {
  height: 6px;
  background:#4c8dee;
  width: 30px;
  bottom: -20px;
  /* top: 0px; */
  left: 50%;
  border-radius:999px;
  content: '';
  position: absolute;
  margin: 0 auto;
  transform: translateX(-50%);
}

.open_beta .article::after,
.turbo .article::after,
.simitone .article::after {
  background: #d3d3d3;
}

.open_beta .article-excerpt,
.turbo .article-excerpt,
.simitone .article-excerpt {
  color: rgba(0,0,0,0.45);
}