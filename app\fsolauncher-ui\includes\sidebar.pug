//--------------------------------------------------------------------------------
  sidebar.pug
  --------------------------------------------------------------------------------
  Contains the sidebar UI and page triggers that trigger navigation.

ul#sidebar
  li#logo
    div
  div#simitone-gradient
  button.launch #{PLAY}
  #simtime-container(title=INGAME_TIME)
    i.material-icons access_time
    #simtime 00:00 AM
  li.active(page-trigger='home')
    i.material-icons.left home
    |  #{HOME}
  li(page-trigger='installer')
    i.material-icons.left package_2
    |  #{INSTALLER}
  li(page-trigger='downloads')
    i.material-icons.left get_app
    |  #{DOWNLOADS}
  li(page-trigger='settings')
    i.material-icons.left settings
    |  #{SETTINGS}
  li(page-trigger='notifications')
    i.material-icons.left notifications
    |  #{NOTIFICATIONS}

  div.bottom
    li(page-trigger='simitone')
      svg.simitone-icon(version='1.0' xmlns='http://www.w3.org/2000/svg' width='109.000000pt' height='149.000000pt' viewBox='0 0 109.000000 149.000000' preserveAspectRatio='xMidYMid meet')
        metadata
          | Created by potrace 1.16, written by Peter Selinger 2001-2019
        g(transform='translate(0.000000,149.000000) scale(0.100000,-0.100000)' fill='#000000' stroke='none')
          path(d='M497 1452 c-55 -84 -82 -219 -73 -375 6 -102 12 -143 26 -162 5 -5\
          18 -48 31 -93 23 -85 22 -104 -5 -202 -3 -8 -5 -18 -5 -21 -1 -4 -36 -8 -79\
          -10 -124 -6 -211 -57 -314 -186 -21 -27 -43 -65 -44 -78 0 -5 -9 -36 -18 -68\
          -11 -36 -17 -80 -14 -115 2 -48 8 -61 33 -84 43 -39 105 -53 235 -53 109 0\
          119 2 182 32 92 44 160 110 204 199 32 65 37 82 41 176 8 151 -15 269 -99 523\
          -67 199 -71 215 -72 305 -1 81 2 101 22 138 13 24 25 42 27 40 1 -1 5 -21 8\
          -43 9 -63 29 -149 37 -160 4 -5 13 -38 20 -73 l13 -62 81 -40 c96 -46 337\
          -150 348 -150 4 0 8 9 8 19 0 30 -68 320 -88 376 l-18 49 -179 78 c-145 62\
          -190 77 -231 78 -49 0 -53 -2 -77 -38z')
      |  SIMITONE
    li(page-trigger='about')
      i.material-icons.left help
      |  #{ABOUT}