.simitone h1.jumbotron {
  background-image: url(../../images/simitone_bg.png) !important;
  background-position: -20px 400px !important;
  text-shadow: 2px 2px rgba(0, 0, 0, 0.2) !important;
}

.simitone .article-title,
.simitone #settings-page .form h1,
.simitone #downloads > div .tag h1,
.simitone .modal h1,
#simitone-page strong.gradient,
#simitone-page .page-content h1 {
  background: linear-gradient(
    45deg,
    #2d6e96 0%,
    #1b9a88 50%,
    #05d474 100%
  ) !important;
  -webkit-background-clip: text !important;
  -webkit-text-fill-color: transparent !important;
}
.simitone .article-image::after {
  background: linear-gradient(
    45deg,
    #2d6e96 0%,
    #1b9a88 50%,
    #05d474 100%
  );
}
#simitone-page strong.gradient {
  font-size: 16px !important;
}

#simitone-page .page-content h1 {
  font-size: 1.4rem !important;
  line-height: 1.6rem !important;
}

#simitone-page .simitone-box p.simitone-nix-error {
  text-align: center;
  text-indent: initial !important;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 12px;
  padding-top: 10px !important;
  margin-top: 5px!important;
}

.simitone #logo:after {
  content: 'LAUNCHER' !important;
}
.simitone #logo {
  background-image: url(../../images/simitone_logo.png) !important;
  background-size: contain !important;
  position: relative;
  z-index: 1;
  top: 20px;
  padding-top: 50px !important;
}
/*.simitone li:hover i.simitone-icon,
 .simitone li.active i.simitone-icon {
   filter: opacity(0.5) drop-shadow(0 0 0 #1A9D88) drop-shadow(0 0 0 #1A9D88);
 }*/
.simitone-icon {
  height: 15px;
  width: 15px;
  margin-right: 5px;
}

#simitone-gradient {
  display: none;
  filter: blur(10px);
  background: linear-gradient(
    45deg,
    #2d6e96 0%,
    #1b9a88 50%,
    #05d474 100%
  ) !important;
  position: absolute;
  width: 190px;
  height: 40px;
  top: 30px;
  left: 0;
  right: 0;
  z-index: 0;
  margin: 0 auto;
}

.simitone #simitone-gradient {
  display: block;
}

.simitone #logo:after {
  content: 'NEON';
}

.simitone button,
.simitone .button {
  background: linear-gradient(
    45deg,
    #2d6e96 0%,
    #1b9a88 50%,
    #05d474 100%
  ) !important;
  text-shadow: 2px 2px rgba(0, 0, 0, 0.2) !important;
  transition: background-position 1s !important;
  background-size: 150% !important;
}

.simitone #launcher #sidebar li.active,
.simitone #launcher #sidebar li:not(#logo):hover {
  color: #258f80;
  border-right-color: #1fae86;
}
.simitone #launcher #sidebar li:not(#logo):hover path,
.simitone #launcher #sidebar li.active path {
  fill: #258f80;
}

.simitone button:hover,
.simitone .button:hover {
  background-position: 99% !important;
}

#simitone-teaser {
  background-image: url(../../images/simitone_teaser.png);
  width: 100%;
  height: 75px;
  background-size: cover;
  background-position: bottom;
  border-radius: 8px;
  font-size: 28px;
  color: #fff;
  backdrop-filter: blur(5px);
  text-align: center;
  padding: 7px;
  box-shadow: 0 0 20px 0px rgba(0, 0, 0, 0.25);
}

#simitone-teaser button {
  padding: 20px;
  border-radius: 8px;
}

.simitone-column {
  float: left;
  width: 50%;
  padding: 20px;
  padding-left: 10px;
}
.simitone-image {
  border-radius: 8px;
  box-shadow: 0 0 20px 0px rgba(0, 0, 0, 0.25);
  background-size: cover;
  height: 200px;
  width: 100%;
}
.simitone-requirements ul {
  font-family: Munged;
  font-size: 14px;
  padding: 10px;
  padding-top: 20px;
}
.simitone-requirements ul li {
  margin-bottom: 15px;
}

.simitone-box {
  border-radius: 8px;
  width: 100%;
  display: block;
  padding: 15px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  overflow: hidden;
  font-weight: normal;
}
#simitone-page .simitone-box p {
  padding: 10px !important;
  padding-top: 0px !important;
}
.simitone-error {
  border-color: #c9340767;
  background: #c93507;
  color: #fff !important;
}
#simitone-play-button {
  display: none;
}

#simitone-page p {
  padding: 0 !important;
  margin: 0 !important;
  margin-top: 20px !important;
  text-indent: 2em !important;
  font-size: 0.9rem;
}

.simitone-installed #simitone-install-button {
  display: none !important;
}
.simitone-installed #simitone-play-button {
  display: inline-block !important;
}

#simitone-indicator {
  color: #c93507;
}
#simitone-indicator i:after {
  color: #c93507;
  content: 'highlight_off';
}
#ts1cc-indicator {
  color: #c93507;
}
#ts1cc-indicator i:after {
  color: #c93507;
  content: 'highlight_off';
}

.simitone-installed #simitone-indicator,
.ts1cc-installed #ts1cc-indicator {
  color: #09c878;
}
.simitone-installed #simitone-indicator i:after,
.ts1cc-installed #ts1cc-indicator i:after {
  color: #09c878;
  content: 'check_circle_outline';
}

.ts1cc-installed #simitone-ts1cc-error {
  display: none !important;
}

.simitone .loading .progress {
  background-image: -webkit-linear-gradient(
      -45deg,
      transparent 33%,
      rgba(255, 255, 255, 0.3) 33%,
      rgba(255, 255, 255, 0.3) 66%,
      transparent 66%
    ),
    -webkit-linear-gradient(top, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1)),
    -webkit-linear-gradient(left, #09c878, #09c878);
}

.simitone .download.stopped .loading .progress {
  background-image: none;
  background: #09c878;
}

#simitone-should-update {
  display: none;
  margin-top: 20px;
  background-color: #09c778;
  color: #fff;
  padding: 10px;
  border-radius: 999px;
  text-align: center;
  width: 100%;
  cursor: pointer;
  overflow: hidden;
}

.simitone-should-update #simitone-should-update {
  display: block;
}

#simitone-should-update:hover {
  opacity: 0.75;
}

.simitone #launcher #sidebar li.active:before,
.simitone #launcher #sidebar li:not(#logo):hover:before {
  background: #258f80!important;
}