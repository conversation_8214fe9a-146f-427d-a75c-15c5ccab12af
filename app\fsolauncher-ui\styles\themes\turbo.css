.turbo {
}

.turbo #logo:after {
  content: 'NEON';
}

.turbo button,
.turbo .button {
  background: linear-gradient(
    45deg,
    #3bade3 0%,
    #576fe6 25%,
    #9844b7 51%,
    #ff357f 100%
  );
  transition: background-position 1s !important;
  background-size: 150% !important;
}

.turbo button:hover,
.turbo .button:hover {
  background-position: 99% !important;
}

/*.turbo li:hover i.simitone-icon,
.turbo li.active i.simitone-icon {
  filter: opacity(0.5) drop-shadow(0 0 0 #9844b7) drop-shadow(0 0 0 #9844b7);
}*/

.turbo #logo {
  background-image: url(../../images/logo_turbo.png) !important;
}

.turbo #launcher #sidebar li.active,
.turbo #launcher #sidebar li:not(#logo):hover {
  color: #725dd2;
  border-right-color: #d33c97;
}

.turbo #launcher #sidebar li:not(#logo):hover path,
.turbo #launcher #sidebar li.active path {
  fill: #725dd2;
}

.turbo .hint {
  background-color: #9d43b3;
}

.turbo .hint.arrow-left:before {
  border-right-color: #843697;
}

.turbo .hint.arrow-top:before {
  border-bottom-color: #843697;
}

.turbo .hint.arrow-top:after {
  border-bottom-color: #9d43b3;
}

.turbo .hint.arrow-left:after {
  border-right-color: #9d43b3;
}

.turbo .article-title,
.turbo #settings-page .form h1,
.turbo #downloads > div .tag h1,
.turbo .modal h1 {
  background: linear-gradient(
    45deg,
    #3bade3 0%,
    #576fe6 25%,
    #9844b7 51%,
    #ff357f 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.turbo .article-image::after,
.turbo h1.jumbotron {
  background: linear-gradient( 
    150deg, 
    #3bade3 0%, 
    #576fe6 25%, 
    #9844b7 51%, 
    #ff357f 125% 
  ) !important;
}

.turbo button.turbo-transparent,
.turbo .button.turbo-transparent {
  background: #40a0e3;
  transition: background 0.4s !important;
}

.turbo button.turbo-transparent:hover,
.turbo .button.turbo-transparent:hover {
  background: #7abceb;
}

.turbo #launcher #sidebar li.active:before,
.turbo #launcher #sidebar li:not(#logo):hover:before {
  background: #725dd2!important;
}