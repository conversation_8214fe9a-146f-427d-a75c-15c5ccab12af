const os = require( 'os' );
const packageJson = require( '../package.json' );
const linuxDistro = ( () => {
  if ( process.platform !== 'linux' )
    return { id: null, like: null };

  try {
    const osReleasePath = require( 'path' ).join( '/etc', 'os-release' );
    const data = require( 'fs-extra' ).readFileSync( osReleasePath, 'utf8' );
    const lines = data.split( '\n' );
    const idLine = lines.find( line => line.startsWith( 'ID=' ) );
    const likeLine = lines.find( line => line.startsWith( 'ID_LIKE=' ) );

    const distroId = idLine ? idLine.split( '=' )[ 1 ].trim().replace( /"/g, '' ).toLowerCase() : null;
    const distroLike = likeLine ? likeLine.split( '=' )[ 1 ].trim().replace( /"/g, '' ).toLowerCase() : null;

    return { id: distroId, like: distroLike };
  } catch ( err ) {
    console.error( 'error reading os-release to determine distro', err );
    return { id: null, like: null };
  }
} )();
const isArch = linuxDistro.id === 'arch' || linuxDistro.like === 'arch';
const isDebian = linuxDistro.id === 'debian' || linuxDistro.like === 'debian';
const linuxLibPath = ( () => {
  const arch = os.arch();
  switch ( arch ) {
  case 'x64':
    return '/usr/lib/x86_64-linux-gnu';
  case 'ia32':
  case 'x32':
    return '/usr/lib/i386-linux-gnu';
  case 'arm':
    return '/usr/lib/arm-linux-gnueabihf';
  case 'arm64':
    return '/usr/lib/aarch64-linux-gnu';
  default:
    console.warn( `Unsupported architecture: ${arch}` );
    return '/usr/lib';
  }
} )();
const homeDir = os.homedir();
const appData = ( () => {
  if ( process.platform === 'darwin' ) {
    return `${homeDir}/Library/Application Support/NewSO Launcher`;
  }
  if ( process.platform === 'linux' ) {
    return `${homeDir}/.fsolauncher`;
  }
  return '.';
} )();
const gameLanguages = {
  English: 0,
  French: 3,
  German: 4,
  Italian: 5,
  Spanish: 6,
  Dutch: 7,
  Danish: 8,
  Swedish: 9,
  Norwegian: 10,
  Finnish: 11,
  Hebrew: 12,
  Russian: 13,
  Portuguese: 14,
  Japanese: 15,
  Polish: 16,
  SimplifiedChinese: 17,
  TraditionalChinese: 18,
  Thai: 19,
  Korean: 20,
  Slovak: 21
};
const isTestMode = process.argv.indexOf( '--fl-test-mode' ) !== -1;
const fileLogEnabled = process.argv.indexOf( '--fl-filelog' ) !== -1;
const devToolsEnabled = process.argv.indexOf( '--fl-devtools' ) !== -1;
const version = packageJson.version;
const defaultRefreshRate = 60;
const defaultGameLanguage = 'English';
const dependencies = {
  'FSO': [ 'TSO', ...( [ 'darwin', 'linux' ].includes( process.platform ) ? [ 'Mono', 'SDL' ] : [ 'OpenAL' ] ) ],
  'RMS': [ 'FSO' ],
  'MacExtras': [ 'FSO' ],
  'Simitone': ( [ 'darwin', 'linux' ].includes( process.platform ) ) ? [ 'Mono', 'SDL' ] : []
};
const needInternet = [
  'TSO',
  'FSO',
  'RMS',
  'Simitone',
  'Mono',
  'MacExtras',
  'SDL'
];
const darkThemes = [
  'halloween', 'dark', 'indigo'
];
const components = {
  'TSO': 'The Sims Online',
  'FSO': 'NewSO',
  'OpenAL': 'OpenAL',
  'NET': '.NET Framework',
  'RMS': 'Remesh Package',
  'Simitone': 'Simitone for Windows',
  'Mono': 'Mono Runtime',
  'MacExtras': 'FreeSO MacExtras',
  'SDL': 'SDL2'
};
const versionChecks = {
  remeshPackageUrl: 'https://beta.freeso.org/RemeshPackage',
  updatesUrl: 'https://beta.freeso.org/UpdateCheck',
  interval: 5 * 60 * 1000 // every 5 minutes
};
const links = {
  updateWizardUrl: 'https://beta.freeso.org/update',
  repoNewIssueUrl: 'https://github.com/ItsSim/fsolauncher/issues/new/choose',
  repoViewIssuesUrl: 'https://github.com/ItsSim/fsolauncher/issues',
  repoDocsUrl: 'https://github.com/ItsSim/fsolauncher/wiki',
  repoUrl: 'https://github.com/ItsSim/fsolauncher',
};
const releases = {
  simitoneUrl: 'https://api.github.com/repos/riperiperi/Simitone/releases/latest',
  fsoGithubUrl: 'https://nsoupdates.s3.us-east-2.amazonaws.com/updates/TheNewSimsOnline_latest.zip',
  fsoApiUrl: 'https://newso.thenewsimsonline.com',
};
const resourceCentral = {
  'TheSimsOnline': 'https://newso.thenewsimsonline.com/tso',
  'NewSO': 'https://newso.thenewsimsonline.com/nso',
  'OpenAL': 'https://openal.org/downloads/oalinst.exe',
  '3DModels': 'https://newso.thenewsimsonline.com/3d',
  'Mono': 'https://beta.freeso.org/LauncherResourceCentral/Mono',
  'MacExtras': 'https://newso.thenewsimsonline.com/MacExtras',
  'SDL': 'https://beta.freeso.org/LauncherResourceCentral/SDL',
  'WS': 'https://beta.freeso.org/LauncherResourceCentral/ws',
  'TrendingLots': 'http://localhost:30631/trending-lots',
  'Scenarios': 'https://beta.freeso.org/LauncherResourceCentral/Scenarios',
  'Blog': 'http://localhost:30631/blog',
  'NET': 'https://newso.thenewsimsonline.com/net'
};
const temp = {
  'FSO': `${appData}/temp/artifacts-freeso-%s.zip`,
  'MacExtras': `${appData}/temp/macextras-%s.zip`,
  'Mono': `${appData}/temp/mono-%s.pkg`,
  'RMS': `${appData}/temp/artifacts-remeshes-%s.zip`,
  'SDL': `${appData}/temp/sdl2-%s.dmg`,
  'TSO': {
    path: `${appData}/temp/tsoclient`,
    file: 'client.zip',
    extractionFolder: 'client',
    firstCab: 'TSO_Installer_v1.1239.1.0/Data1.cab',
    rootCab: 'Data1.cab'
  }
};
const registry = {
  ociName: 'NewsO Game',
  paths: {
    'TSO': process.platform === 'win32' ?
      'HKLM\\SOFTWARE\\Maxis\\The Sims Online' :
      `${appData}/GameComponents/The Sims Online/TSOClient/TSOClient.exe`,

    'NSO': process.platform === 'win32' ?
      'HKLM\\SOFTWARE\\Professor Oak\\NewSO' :
      path.join(appData, 'GameComponents', 'NewSO', 'NewSO.exe'),


    'OpenAL': 'HKLM\\SOFTWARE\\OpenAL',
    'NET': 'HKLM\\SOFTWARE\\Microsoft\\NET Framework Setup\\NDP',
    'Mono': process.platform === 'darwin' ? '/Library/Frameworks/Mono.framework' : '/usr/bin/mono',
    'SDL': process.platform === 'darwin' ? '/Library/Frameworks/SDL2.framework' : `${linuxLibPath}/libSDL2-2.0.so.0`
  },
  fallbacks: process.platform === 'win32' ? {
    // Windows fallbacks
    'TSO': [
      'C:/Program Files/Maxis/The Sims Online/TSOClient/TSOClient.exe',
      'C:/Program Files/The Sims Online/TSOClient/TSOClient.exe',
      'C:/Program Files/newSO Game/The Sims Online/TSOClient/TSOClient.exe'
    ],
    'FSO': [
      'C:/Program Files/NewSO/NewSO.exe',
      'C:/Program Files/NewSO Game/newSO/Newso.exe'
    ],
    'OpenAL': [
      'C:/Program Files (x86)/OpenAL'

    ],
    'NSO': [
      'C:/Program Files/NewSO Game/NewSO/NewSO.exe',
      'C:/Program Files/NewSO/NewSO.exe'
    ]
  } : {
    // macOS fallbacks
    'TSO': [
      `${appData}/GameComponents/The Sims Online/TSOClient/TSOClient.exe`,
      `${homeDir}/Documents/The Sims Online/TSOClient/TSOClient.exe`,
    ],
    'FSO': [
      `${appData}/GameComponents/FreeSO/newSO.exe`,
      `${homeDir}/Documents/newSO/NewSO.exe`,
    ],
    'NSO': [
      path.join(appData, 'GameComponents', 'NewSO', 'NewSO.exe'),
      path.join(homeDir, 'Documents', 'NewSO', 'NewSO.exe'),
    ]
  }
};

module.exports = {
  homeDir,
  appData,
  gameLanguages,
  isTestMode,
  fileLogEnabled,
  devToolsEnabled,
  version,
  defaultRefreshRate,
  defaultGameLanguage,
  dependencies,
  needInternet,
  darkThemes,
  components,
  versionChecks,
  links,
  releases,
  resourceCentral,
  temp,
  registry,
  linuxDistro,
  linuxLibPath,
  isArch,
  isDebian
};

/**
 * Switches the game language.
 * Copies the translation files and changes the current language in FreeSO.ini.
 *
 * @param {string} langString The language to change to. Example: 'English', 'Spanish'.
 *
 * @returns {Promise<void>} A promise that resolves when the language is changed.
 */
async function switchGameLanguage(langString) {
  // Ensure langString exists and is properly capitalized
  if (!langString) {
    langString = 'English'; // Default fallback
  }

  // First character uppercase, rest lowercase
  langString = langString.charAt(0).toUpperCase() + langString.slice(1).toLowerCase();

  // Get the language code from gameLanguages mapping
  const langCode = gameLanguages[langString];
  if (langCode === undefined) {
    console.error(`Invalid language: ${langString}`);
    throw new Error(`Invalid language: ${langString}`);
  }

  if (!this.isInstalled.NSO) {
    throw new Error(locale.current.GAME_NOT_INSTALLED);
  }

  // Path to config.ini
  const configPath = path.join(this.isInstalled.NSO, 'Content', 'config.ini');

  try {
    // Read the current config
    let configContent = await fs.readFile(configPath, 'utf8');

    // Update the language setting
    configContent = configContent.replace(
      /CurrentLang=.*/,
      `CurrentLang=${langString.toLowerCase()}`
    );

    // Write the updated config back
    await fs.writeFile(configPath, configContent, 'utf8');

    // Update the launcher settings
    if (this.userSettings && this.userSettings.Game) {
      this.userSettings.Game.Language = langString;
      await this.persist();
    }

    // Notify success
    this.IPC.sendLanguageChanged(langString);
  } catch (err) {
    console.error('Language switch error:', err);
    captureWithSentry(err, { langString, langCode });
    throw new Error(locale.current.FAILED_CHANGE_LANGUAGE);
  }
}






