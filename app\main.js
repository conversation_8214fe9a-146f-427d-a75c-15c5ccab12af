require('fix-path')(); // Fix $PATH on darwin
require('v8-compile-cache');

const path = require('path');
const fs = require('fs-extra');
const os = require('os');
const { spawn } = require('child_process');

// 🔧 Setup the Electron cache/userData path BEFORE requiring 'electron'
const userDataPath = path.join(os.homedir(), 'AppData', 'Roaming', 'NewSO_Launcher_UserData');
const cachePath = path.join(userDataPath, 'cache');

try {
  fs.ensureDirSync(userDataPath);
  fs.ensureDirSync(cachePath);
  process.env.ELECTRON_USER_DATA_PATH = userDataPath;
} catch (err) {
  console.error('Failed to create Electron data path:', err);
}

const {
  app,
  BrowserWindow,
  shell,
  Tray,
  Menu,
  nativeImage,
  nativeTheme
} = require('electron');

const { initSentry, enableFileLogger } = require('./fsolauncher/lib/utils');

// Disable GPU cache if we still have issues
app.commandLine.appendSwitch('disable-gpu-cache');

// init Sentry error logging as soon as possible
initSentry();

const compilePugFiles = require('./fsolauncher/lib/pug-compiler');

const {
  appData,
  version,
  darkThemes,
  resourceCentral,
  isTestMode,
  fileLogEnabled,
  devToolsEnabled,
  defaultRefreshRate,
  defaultGameLanguage,
  homeDir
} = require('./fsolauncher/constants');

if (fileLogEnabled) {
  enableFileLogger();
  console.info('file logger enabled');
}

if (isTestMode && process.platform !== 'linux') {
  app.disableHardwareAcceleration();
  app.commandLine.appendSwitch('no-sandbox');
  app.commandLine.appendSwitch('disable-gpu');
  app.commandLine.appendSwitch('disable-software-rasterizer');
  app.commandLine.appendSwitch('disable-gpu-compositing');
  app.commandLine.appendSwitch('disable-gpu-rasterization');
  app.commandLine.appendSwitch('disable-gpu-sandbox');
  app.commandLine.appendSwitch('--no-sandbox');
}

const { locale, setLocale } = require('./fsolauncher/lib/locale');

const oslocale = require('os-locale'),
  ini = require('ini');

const FSOLauncher = require('./fsolauncher/fsolauncher');

process.title = 'NewSO Launcher';

global.willQuit = false;

const prevOpenExternal = shell.openExternal;
shell.openExternal = Object.freeze(url => {
  if (url.startsWith('http') || url.startsWith('https')) {
    prevOpenExternal(url);
  }
});
Object.freeze(shell);

fs.ensureDirSync(appData + '/temp');

let window;
let tray;
let launcher;
let trayIcon;
let userSettings;
let proxyProcess;

try {
  userSettings = ini.parse(fs.readFileSync(appData + '/FSOLauncher.ini', 'utf-8'));
} catch (err) {
  userSettings = {
    Launcher: {
      Theme: 'auto',
      DesktopNotifications: '1',
      Persistence: ['darwin', 'linux'].includes(process.platform) ? '0' : '1',
      DirectLaunch: '0',
      Language: 'default'
    },
    Game: {
      GraphicsMode: process.platform === 'win32' ? 'dx' : 'ogl',
      Language: defaultGameLanguage
    }
  };
  fs.writeFileSync(appData + '/FSOLauncher.ini', ini.stringify(userSettings), 'utf-8');
}
console.info('loaded userSettings', userSettings);

function loadLocale(settings) {
  let langCode = settings.Launcher.Language;
  if (!langCode || langCode === 'default') {
    langCode = oslocale.sync().substring(0, 2);
  }
  setLocale(langCode, {
    CSP_STRING: require('./csp.config'),
    LAUNCHER_VERSION: version,
    ELECTRON_VERSION: process.versions.electron,
    LAUNCHER_THEME: settings.Launcher.Theme === 'auto' ? nativeTheme.shouldUseDarkColors ? 'dark' : 'open_beta' : settings.Launcher.Theme,
    PLATFORM: process.platform,
    DARK_THEMES: darkThemes.join(','),
    SENTRY: require('./sentry.config').browserLoader,
    LANG_CODE: langCode,
    DEFAULT_REFRESH_RATE: defaultRefreshRate,
    REMESH_PACKAGE_CREDITS: require('./fsolauncher-ui/remesh-package.json'),
    PRELOADED_FONTS: require('./fonts.config'),
    WS_URL: resourceCentral.WS,
    TRENDING_LOTS_URL: resourceCentral.TrendingLots,
    SCENARIOS_URL: resourceCentral.Scenarios,
    SIMITONE_PLATFORM_PATH: appData.replace(homeDir, '~') + '/GameComponents/The Sims',
    BLOG_URL: resourceCentral.Blog
  });
}
loadLocale(userSettings);

const options = {};

function showWindow() {
  console.info('Showing window, isTestMode:', isTestMode);
  if (!window) {
    console.error('Cannot show window: window is null');
    return;
  }
  if (isTestMode) {
    console.info('Test mode active, not showing window');
  } else {
    console.info('Showing window');
    window.show();
  }
}

function startProxyServer() {
  try {
    // In development, proxy is at ../extras/fsolauncher-proxy
    // In packaged app, proxy is in app.getPath('userData')/extras/fsolauncher-proxy or process.resourcesPath
    let proxyPath;

    if (app.isPackaged) {
      proxyPath = path.join(process.resourcesPath, 'extras', 'fsolauncher-proxy');
    } else {
      proxyPath = path.join(__dirname, '../extras/fsolauncher-proxy');
    }

    console.info('Starting proxy server from:', proxyPath);

    // Check if proxy directory exists
    if (!fs.existsSync(proxyPath)) {
      console.error('Proxy server directory not found:', proxyPath);
      return startProxyServerAlternative();
    }

    // Skip npm approach and directly use Node.js
    return startProxyServerAlternative();
  } catch (error) {
    console.error('Failed to start proxy server:', error);
    startProxyServerAlternative();
  }
}

function startProxyServerAlternative() {
  try {
    // Use the same path logic as the main function
    let proxyPath;

    if (app.isPackaged) {
      proxyPath = path.join(process.resourcesPath, 'extras', 'fsolauncher-proxy');
    } else {
      proxyPath = path.join(__dirname, '../extras/fsolauncher-proxy');
    }

    const nodePath = process.execPath; // Path to the Node.js executable used by Electron
    const scriptPath = path.join(proxyPath, 'src/index.js');

    console.info('Starting proxy server with Node.js directly:', nodePath);

    proxyProcess = spawn(nodePath, [scriptPath], {
      cwd: proxyPath,
      stdio: ['ignore', 'pipe', 'pipe'],
      detached: false,
      env: { ...process.env }
    });

    proxyProcess.stdout.on('data', (data) => {
      console.info('Proxy server (alt):', data.toString().trim());
    });

    proxyProcess.stderr.on('data', (data) => {
      console.error('Proxy server error (alt):', data.toString().trim());
    });

    proxyProcess.on('close', (code) => {
      console.info(`Proxy server (alt) exited with code ${code}`);
    });

    proxyProcess.on('error', (error) => {
      console.error('Failed to start proxy server with alternative method:', error);
    });

    console.info('Proxy server (alt) started with PID:', proxyProcess.pid);
  } catch (error) {
    console.error('Alternative proxy server startup failed:', error);
  }
}

async function createWindow() {
  try {
    console.info('Starting window creation process');
    compilePugFiles({ pretty: false }, () => locale.current);
    console.info('Pug files compiled');

    trayIcon = nativeImage.createFromPath(
      path.join(__dirname, ['darwin', 'linux'].includes(process.platform) ? 'beta.png' : 'beta.ico')
    );
    if (['darwin', 'linux'].includes(process.platform)) {
      trayIcon = trayIcon.resize({ width: 16, height: 16 });
    }
    tray = new Tray(trayIcon);
    console.info('Tray icon created');

    const width = 1090 + 8;
    const height = 646 + 12 + 30;

    Object.assign(options, {
      transparent: true,
      minWidth: width,
      minHeight: height,
      maxWidth: width,
      maxHeight: height,
      center: true,
      maximizable: false,
      width: width,
      height: height,
      useContentSize: true,
      show: false,
      frame: false,
      resizable: false,
      title: 'NewSO Launcher ' + version,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        offscreen: isTestMode,
        preload: path.join(__dirname, './fsolauncher-ui/preload.js')
      }
    });

    console.info('Creating browser window with options:', options);
    window = new BrowserWindow(options);
    window.setMenu(null);

    if (devToolsEnabled && !isTestMode) {
      console.info('devtools enabled');
      window.openDevTools({ mode: 'detach' });
    }

    console.info('Loading URL:', `file://${__dirname}/fsolauncher-ui/fsolauncher.pug`);
    window.loadURL(`file://${__dirname}/fsolauncher-ui/fsolauncher.pug`);

    window.on('hide', () => process.platform === 'win32' && window.setSize(width, height));
    window.on('restore', () => process.platform === 'win32' && window.setSkipTaskbar(false));

    console.info('Creating FSOLauncher instance');
    launcher = new FSOLauncher({
      window,
      userSettings,
      onReload: async settings => {
        loadLocale(settings);
        window.reload();
      }
    });

    if (process.platform === 'darwin') {
      const darwinAppMenu = require('./fsolauncher/lib/darwin-app-menu');
      Menu.setApplicationMenu(Menu.buildFromTemplate(darwinAppMenu(app.getName(), launcher)));
    }

    tray.setToolTip(`NewSO Launcher ${version}`);
    tray.setContextMenu(Menu.buildFromTemplate([
      {
        label: locale.current.TRAY_LABEL_1,
        click: () => launcher.launchGame()
      },
      { type: 'separator' },
      {
        label: locale.current.TRAY_LABEL_2,
        click: () => {
          global.willQuit = true;
          window?.close();
        }
      }
    ]));

    tray.on('click', () => {
      if (!window) return;
      if (window.isVisible()) {
        if (['darwin', 'linux'].includes(process.platform)) {
          window.minimize();
        } else {
          window.hide();
        }
      } else {
        showWindow();
      }
    });

    window.on('closed', () => window = null);

    window.once('ready-to-show', () => {
      console.info('Window ready to show');
      launcher.updateInstalledPrograms().then(() => {
        console.info('Programs updated, DirectLaunch:', userSettings.Launcher.DirectLaunch, 'NSO installed:', launcher.isInstalled.NSO);
        if (userSettings.Launcher.DirectLaunch === '1' && launcher.isInstalled.NSO) {
          launcher.launchGame();
          if (['darwin', 'linux'].includes(process.platform)) {
            showWindow();
          }
        } else {
          showWindow();
        }
      }).catch(err => {
        console.error('Error updating installed programs:', err);
        showWindow();
      });
    });

    window.on('close', e => {
      if (!global.willQuit && launcher.userSettings.Launcher.Persistence === '1') {
        e.preventDefault();
        window.minimize();
      }
    });

    window.webContents.setWindowOpenHandler(({ url }) => {
      shell.openExternal(url);
      return { action: 'deny' };
    });

    // Add error handler for window loading
    window.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
      console.error('Failed to load window content:', errorCode, errorDescription);
    });
  } catch (error) {
    console.error('Error in createWindow:', error);
  }
}

app.on('ready', () => {
  console.info('App ready event triggered');
  startProxyServer();
  createWindow().catch(err => {
    console.error('Failed to create window:', err);
  });
});

app.on('before-quit', () => {
  tray && tray.destroy();
  if (proxyProcess && !proxyProcess.killed) {
    console.info('Stopping proxy server...');
    proxyProcess.kill();
  }
});

app.on('window-all-closed', () => app.quit());

app.on('activate', () => window === null && createWindow());

const gotTheLock = app.requestSingleInstanceLock();
if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', (_event, _commandLine, _workingDirectory) => {
    if (window) {
      showWindow();
      window.focus();
    }
  });
}
