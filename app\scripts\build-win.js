const packager = require('@electron/packager').packager;
const { execSync } = require('child_process');

(async () => {
  try {
    await packager({
      dir: '.',
      name: 'nsolauncher',
      out: '../release',
      platform: 'win32',
      arch: 'ia32',
      icon: './beta.ico',
      extraResource: ['./cache'],
      asar: {
        unpackDir: '{fsolauncher-ui/images,fsolauncher-ui/sounds,fsolauncher-ui/fonts,cache}',
      },
      overwrite: true,
      appCopyright: 'Copyright (C) NewSO. All rights reserved.',
      win32metadata: {
        CompanyName: 'TheNewSimsOnline.com',
        'requested-execution-level': 'requireAdministrator',
        FileDescription: 'NewSO Launcher',
      },
      derefSymlinks: true
    });
    execSync('npm run copywin', { stdio: 'inherit' });
    execSync('npm run compileiss', { stdio: 'inherit' });
  } catch (err) {
    console.error(err);
    process.exitCode = 1;
  }
})();
