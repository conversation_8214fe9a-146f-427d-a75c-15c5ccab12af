#define MyAppName "NewSo Launcher"
#define MyAppVersion "1.12.1"
#define MyAppPublisher "TheNewSimsOnline.com"
#define MyAppURL "https://TheNewSimsOnline.com"
#define MyAppExeName "NewSoLauncher.exe"

[Setup]
AppId={{f202fe96-34bb-4d52-b4d5-8429f43a94b2}
AppName={#MyAppName}
AppVersion={#MyAppVersion}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
AppCopyright=Copyright (C) NewSo. All rights reserved.
DefaultDirName={pf}\NewSO Game\{#MyAppName}
DefaultGroupName={#MyAppName}
DisableProgramGroupPage=yes
OutputDir=.
OutputBaseFilename={#MyAppName} Setup
SetupIconFile=.\nsolauncher-win32-ia32\beta.ico
InfoBeforeFile=.\nsolauncher-win32-ia32\readme.txt
WizardStyle=modern
Compression=lzma
SolidCompression=yes
CloseApplications=force
TimeStampsInUTC=yes
PrivilegesRequired=admin
UsePreviousAppDir=no

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: ".\\nsolauncher-win32-ia32\\nsolauncher.exe"; DestDir: "{app}"; DestName: "{#MyAppExeName}"; Flags: ignoreversion
Source: ".\\nsolauncher-win32-ia32\\*"; DestDir: "{app}"; Excludes: "nsolauncher.exe"; Flags: ignoreversion recursesubdirs createallsubdirs

[Icons]
Name: "{commonprograms}\\{#MyAppName}"; Filename: "{app}\\{#MyAppExeName}"
Name: "{commondesktop}\\{#MyAppName}"; Filename: "{app}\\{#MyAppExeName}"; Tasks: desktopicon


[Run]
Filename: "{app}\\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait skipifsilent
